# Migração do Sistema Learning para Banco Separado

## 📖 Visão Geral

O sistema de learning do DataHero4 foi migrado para usar um banco de dados PostgreSQL separado, isolando as operações de aprendizado do banco principal do cliente.

## 🎯 Motivação

- **Isolamento**: Cliente reclamou de tabelas sendo criadas no banco principal
- **Separação de Responsabilidades**: Learning deve usar infraestrutura isolada
- **Manutenção**: Facilita manutenção e backup de dados de learning
- **Segurança**: Reduz risco de impacto no banco principal

## 🔧 Mudanças Implementadas

### 1. Configuração de Ambiente

O sistema agora usa as seguintes variáveis de ambiente para o banco de learning:

```bash
# Banco de dados de Learning (Railway PostgreSQL)
DB_LEARNING_HOST=switchyard.proxy.rlwy.net
DB_LEARNING_PORT=51728
DB_LEARNING_USER=postgres
DB_LEARNING_PASSWORD=HraspFPwwponJFCklBwrvpcRNrGePXxw
DB_LEARNING_NAME=railway
DATABASE_URL_LEARNING="postgresql://postgres:<EMAIL>:51728/railway"
```

### 2. Alterações no Código

#### `src/utils/learning_db_utils.py`

- **LearningDBManager**: Atualizado para usar configurações específicas do banco learning
- **Variáveis padrão**: Mudadas para usar `DB_LEARNING_*` em vez de `L2M_DB_*`
- **Porta padrão**: Alterada para 40846 (específica do banco learning)
- **Host padrão**: Alterado para `tramway.proxy.rlwy.net`

#### Principais mudanças:

```python
# ANTES
self.host = host or os.getenv('POSTGRES_HOST', 'l2m-homol.c50woq6q6gnz.us-east-1.rds.amazonaws.com')
self.port = port
self.database = database or os.getenv('POSTGRES_DB', 'l2m_prod')

# DEPOIS (Atualizado para Railway)
self.host = host or os.getenv('DB_LEARNING_HOST', 'switchyard.proxy.rlwy.net')
self.port = port or int(os.getenv('DB_LEARNING_PORT', '51728'))
self.database = database or os.getenv('DB_LEARNING_NAME', 'railway')
```

## 🧪 Validação

### Teste de Conectividade

O sistema foi testado e validado:

```bash
✅ Connected to: switchyard.proxy.rlwy.net:51728/railway
```

### Tabelas Criadas

As seguintes tabelas de learning foram criadas no banco separado:

- `feedback_corrections`: 0 records
- `correction_patterns`: 0 records  
- `query_cache`: 0 records
- `pattern_applications`: 0 records
- `query_history`: 0 records
- `query_embeddings`: 0 records

## 🔄 Componentes Afetados

### Automáticos (sem mudança necessária)

Todos os componentes que usam `get_db_manager()` foram automaticamente migrados:

- `src/nodes/learning_update_node.py`
- `src/nodes/cache_lookup_node.py`
- `src/interfaces/api.py`
- `src/optimization/lazy_loader.py`

### Funcionais

- ✅ **Feedback Store**: Agora armazena dados no banco learning
- ✅ **Query Cache**: Cache de queries isolado
- ✅ **Pattern Library**: Padrões armazenados separadamente
- ✅ **Similarity Engine**: Embeddings no banco correto

## 🚀 Deploy

### Pré-requisitos

1. ✅ Banco de dados learning já criado e configurado
2. ✅ Variáveis de ambiente `DB_LEARNING_*` configuradas
3. ✅ Conectividade validada

### Processo

1. ✅ Código atualizado em `src/utils/learning_db_utils.py`
2. ✅ Testes de conectividade executados
3. ✅ Validação de componentes realizada
4. ✅ Documentação criada

## 🎛️ Rollback

Em caso de problemas, para voltar ao banco anterior:

1. Reverter mudanças em `src/utils/learning_db_utils.py`
2. Usar variáveis `L2M_DB_*` originais
3. Executar testes de conectividade

## 📝 Próximos Passos

1. **Monitoramento**: Verificar se não há impactos em produção
2. **Performance**: Validar que a performance se mantém
3. **Backup**: Configurar backup do novo banco learning
4. **Migração de Dados**: Se necessário, migrar dados existentes

---

**Data da Migração**: 2025-06-05  
**Status**: ✅ Concluída com sucesso 