[tool.poetry]
name = "datahero4"
version = "0.1.0"
description = "Sistema Multi-Agentes para Análise de Dados Multissetorial"
authors = ["Your Name <<EMAIL>>"]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
langchain = "^0.3.24"
langchain-core = "^0.3.56"
langchain-openai = "^0.3.15"
langchain-anthropic = "^0.3.12"
langchain-community = "^0.3.23"
langchain-together = "^0.3.0"
langgraph = "^0.3.34"
langchain-text-splitters = "^0.3.8"
langgraph-sdk = "^0.1.63"
together = "^1.5.7"
anthropic = "^0.51.0"
openai = "^1.77.0"
langsmith = "^0.3.37"
fastapi = "^0.115.12"
uvicorn = "^0.34.2"
pydantic = "^2.11.3"
pydantic-settings = "^2.9.1"
python-dotenv = "^1.1.0"
typer = "^0.15.3"
psycopg2-binary = "^2.9.10"
PyYAML = "^6.0.2"
pandas = "^2.2.3"
rich = "^14.0.0"
sqlglot = "^26.16.4"
tiktoken = "^0.9.0"
pytest = "^8.3.5"
pytest-cov = "^6.1.1"
redis = "^5.0.1"
twilio = "^9.0.1"
flask = "^3.0.3"
langgraph-checkpoint = "^2.0.25"
sqlalchemy = "^2.0.40"
requests = "^2.32.3"
langchain-google-genai = "^2.1.5"
psutil = "^7.0.0"
# ML dependencies (required for production)
sentence-transformers = "^4.1.0"
scikit-learn = "^1.6.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-cov = "^6.1.1"

[tool.poetry.group.ml]
optional = true

[tool.poetry.group.ml.dependencies]
sentence-transformers = "^4.1.0"
scikit-learn = "^1.6.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
addopts = "-ra --ignore=tests_legacy --ignore=src/tests"
norecursedirs = ["tests_legacy", "src/tests"] 