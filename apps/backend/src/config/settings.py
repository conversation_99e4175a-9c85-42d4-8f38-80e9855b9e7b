"""
Configurações da aplicação carregadas do ambiente usando Pydantic Settings.
"""

from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Carrega variáveis de ambiente. Procura por um arquivo .env.

    Prioridade:
    1. Variáveis de ambiente do sistema.
    2. Variáveis definidas no arquivo .env.
    3. Valores padrão definidos aqui.
    """
    # Configuração do Pydantic Settings
    model_config = SettingsConfigDict(
        # `.env_example` é um bom padrão, mas `../.env` funciona melhor se
        # o ponto de entrada estiver em `src`
        env_file=('.env', '../.env'), # Procura .env no diretório atual e no pai
        env_file_encoding='utf-8',
        extra='ignore' # Ignora variáveis extras no .env
    )

    # Credenciais do Banco de Dados (Exemplo PostgreSQL)
    DB_USER: str = Field(default="user", description="Usuário do banco de dados")
    DB_PASSWORD: str = Field(default="password", description="Senha do banco de dados")
    DB_HOST: str = Field(default="localhost", description="Host do banco de dados")
    DB_PORT: int = Field(default=5432, description="Porta do banco de dados")
    DB_NAME: str = Field(default="mydatabase", description="Nome do banco de dados")
    
    # Credenciais do Banco de Learning (PostgreSQL Railway)
    DB_LEARNING_HOST: str = Field(default="switchyard.proxy.rlwy.net", description="Host do banco de learning")
    DB_LEARNING_PORT: int = Field(default=51728, description="Porta do banco de learning")
    DB_LEARNING_USER: str = Field(default="postgres", description="Usuário do banco de learning")
    DB_LEARNING_PASSWORD: Optional[str] = Field(default=None, description="Senha do banco de learning")
    DB_LEARNING_NAME: str = Field(default="railway", description="Nome do banco de learning")

    # Chaves de API LLM (adicionar provedores conforme necessário)
    TOGETHER_API_KEY: Optional[str] = Field(default=None, description="Chave API Together AI")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, description="Chave API Anthropic")
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="Chave API OpenAI")
    FIREWORKS_API_KEY: Optional[str] = Field(default=None, description="Chave API Fireworks AI")
    GROQ_API_KEY: Optional[str] = Field(default=None, description="Chave API Groq")
    GOOGLE_API_KEY: Optional[str] = Field(default=None, description="Chave API Google Gemini")

    # Configurações Opcionais LangSmith
    LANGCHAIN_TRACING_V2: Optional[str] = Field(default=None, description="Habilitar tracing LangSmith")
    LANGCHAIN_ENDPOINT: Optional[str] = Field(default="https://api.smith.langchain.com", description="Endpoint LangSmith")
    LANGCHAIN_API_KEY: Optional[str] = Field(default=None, description="Chave API LangSmith")
    LANGCHAIN_PROJECT: Optional[str] = Field(default=None, description="Nome do projeto LangSmith")

    # Outras configurações da aplicação
    MAX_QUERY_ATTEMPTS: int = Field(default=3, description="Máximo de tentativas de refinamento da query SQL")
    USE_REFINED_OUTPUT: bool = Field(default=True, description="Habilita o novo formato de saídas refinadas (resposta-síntese, insight principal, 2 sugestões)")
    

    
    # Sistema sempre usa workflow otimizado + Nível 2 (configuração hardcoded)

# Função para carregar e retornar as configurações
# O Pydantic Settings instancia automaticamente ao importar a classe
# Mas uma função pode ser útil para clareza ou lógica adicional
_cached_settings: Optional[Settings] = None

def load_settings() -> Settings:
    """Carrega as configurações do ambiente e as retorna.

    Usa um cache simples para evitar recarregar do .env repetidamente.
    """
    global _cached_settings
    if _cached_settings is None:
        try:
            _cached_settings = Settings()
            # logger.debug("Configurações carregadas com sucesso.")
            # Não logar settings diretamente para evitar expor senhas/chaves
        except Exception as e:
            logger.error(f"Erro ao carregar configurações: {e}", exc_info=True)
            raise
    return _cached_settings

# Exemplo de como usar:
# if __name__ == "__main__":
#     from dotenv import load_dotenv
#     load_dotenv()
#     settings = load_settings()
#     print("Configurações carregadas:")
#     print(f"DB Host: {settings.DB_HOST}")
#     # print(settings.model_dump()) # Cuidado ao imprimir tudo 