"""DataHero Unified API - Production-ready with all features."""

import os
import uuid
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Core imports
from src.graphs.optimized_workflow import create_optimized_workflow
graph = create_optimized_workflow()
from src.models.feedback import (
    FeedbackCorrection, FeedbackResponse, FeedbackSummary,
    create_feedback_from_natural_language, FeedbackSource
)
from src.utils.learning_db_utils import LearningDBManager, get_db_manager
from src.models.learning_models import FeedbackCorrection as PGFeedbackCorrection
# from src.utils.frontend_processor import frontend_processor  # Não mais necessário - LLM gera diretamente
from sqlalchemy import text

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI
app = FastAPI(
    title="DataHero API",
    description="DataHero LangGraph API with Learning and Feedback",
    version="4.0.0"
)

# -------------------------------------------------
# CORS Configuration
# -------------------------------------------------
# The allowed origins can be controlled via the `CORS_ALLOW_ORIGINS` environment
# variable (comma-separated list). If the variable is not set, we fall back to a
# sane default that works for local development and production.
origins_env = os.getenv("CORS_ALLOW_ORIGINS")

if origins_env:
    # Split by comma, remove surrounding whitespace and discard empty strings
    origins = [o.strip() for o in origins_env.split(",") if o.strip()]
else:
    origins = [
        "http://localhost:8080",  # Vite dev server (default port)
        "http://localhost:8081",  # React dev server / alternative
        "http://localhost:3000",  # Vite dev server (default port)
        "https://frontend-production-324f.up.railway.app",  # Production frontend
    ]

# Apply CORS middleware with the configured origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files will be mounted at the end of the file after all API routes are defined

# Initialize PostgreSQL learning system
try:
    db_manager = get_db_manager()
    LEARNING_SYSTEMS_AVAILABLE = True
    logger.info("✅ PostgreSQL learning system initialized successfully")
except Exception as e:
    db_manager = None
    LEARNING_SYSTEMS_AVAILABLE = False
    logger.error(f"❌ PostgreSQL learning system failed to initialize: {e}")


# Request/Response Models
class AskRequest(BaseModel):
    """Request model for asking questions."""
    question: str = Field(..., min_length=3, description="User question")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")
    channel: str = Field(default="api", description="Channel (api/cli/whatsapp)")
    session_id: Optional[str] = Field(None, description="Session ID for conversation tracking")


class FrontendDisplay(BaseModel):
    """Frontend display structure for UI presentation."""
    question_summary: str = Field(..., description="Summary of the question")
    direct_answer: str = Field(..., description="Direct answer to display as title")
    detailed_explanation: str = Field(..., description="Detailed explanation (2-4 lines)")


class AskResponse(BaseModel):
    """Response model for ask endpoint."""
    query_id: str = Field(..., description="Unique query identifier")
    question: str = Field(..., description="Original question")
    sql_query: Optional[str] = Field(None, description="Generated SQL query")
    results: Optional[Any] = Field(None, description="Query execution results")
    business_analysis: Optional[Dict[str, Any]] = Field(None, description="Complete business analysis")
    insights: Optional[List[str]] = Field(default_factory=list, description="Business insights")
    suggestions: Optional[List[str]] = Field(default_factory=list, description="Follow-up suggestions")
    formatted_response: Optional[str] = Field(None, description="Formatted text response")
    frontend_display: Optional[FrontendDisplay] = Field(None, description="Frontend-specific display")
    visualization_data: Optional[Dict[str, Any]] = Field(None, description="Chart/visualization data")
    error: Optional[str] = Field(None, description="Error message if any")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class FeedbackRequest(BaseModel):
    """Request model for submitting feedback."""
    query_id: str = Field(..., description="ID of query being corrected")
    feedback_text: str = Field(..., min_length=3, description="Feedback in natural language")
    original_question: str = Field(..., description="Original question asked")
    corrected_sql: Optional[str] = Field(None, description="Corrected SQL query")
    user_id: Optional[str] = Field(None, description="User providing feedback")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")


class SimpleFeedbackRequest(BaseModel):
    """Request model for simple positive/negative feedback."""
    query_id: str = Field(..., description="ID of query being rated")
    feedback_type: str = Field(..., description="positive or negative")
    original_question: str = Field(..., description="Original question asked")
    category: Optional[str] = Field(None, description="Feedback category for negative feedback")
    comment: Optional[str] = Field(None, description="Additional comment")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")
    user_id: Optional[str] = Field(None, description="User providing feedback")


class FeedbackReprocessRequest(BaseModel):
    """Request model for feedback submission with immediate reprocessing."""
    feedback: SimpleFeedbackRequest = Field(..., description="Feedback data")
    reprocess: bool = Field(default=True, description="Whether to reprocess immediately")


class ReprocessRequest(BaseModel):
    """Request to reprocess a question with learned corrections."""
    original_question: str = Field(..., description="Question to reprocess")
    query_id: Optional[str] = Field(None, description="Reference to original query")
    feedback_id: Optional[str] = Field(None, description="Specific feedback to apply")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")
    apply_recent_feedback: bool = Field(default=True, description="Apply recent feedback patterns")


class HealthResponse(BaseModel):
    """Health check response."""
    status: str = Field(..., description="System health status")
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = Field(default="4.0.0")
    database: Dict[str, Any] = Field(default_factory=dict)
    systems: Dict[str, Any] = Field(default_factory=dict)
    feature_flags: Dict[str, bool] = Field(default_factory=dict)


# Helper Functions
def build_initial_state(request: AskRequest) -> Dict[str, Any]:
    """Build initial state for pipeline execution."""
    return {
        "messages": [],
        "question": request.question,
        "sql_query": None,
        "sql_valid": False,
        "validation_errors": [],
        "query_result": None,
        "business_analysis": None,
        "insights": None,
        "suggestions": [],
        "kpi_context": {},
        "schema": {},
        "attempts": 0,
        "user_role": None,
        "client_id": request.client_id,
        "sector": request.sector,
        "channel": request.channel,
        "session_id": request.session_id,
        "schema_relevance_path": f"src/config/setores/{request.sector}/{request.client_id}/{request.client_id}_schema_relevance.json",
        "sector_kpi_path": f"src/config/setores/{request.sector}/kpis-exchange-json.json",
        "llm_config_path": f"src/config/setores/{request.sector}/{request.client_id}/llm.yaml",
        "prompt_path": f"src/config/setores/{request.sector}/{request.client_id}/prompts/query_generator.txt",
        "prompt_path_business_analyst": f"src/config/setores/{request.sector}/{request.client_id}/prompts/business_analyst.txt",
        "thread_id": f"api-{uuid.uuid4()}",
    }


# Main Endpoints
@app.post("/ask", response_model=AskResponse)
async def ask(request: AskRequest):
    """Process a question and return structured response."""
    try:
        # Generate query ID
        query_id = f"QRY_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # Build state
        state = build_initial_state(request)
        state["query_id"] = query_id
        
        # Execute pipeline
        logger.info(f"Processing question: {request.question}")
        final_state = graph.invoke(state, config={"thread_id": state["thread_id"]})
        
        logger.info(f"Final state keys: {list(final_state.keys())}")
        logger.info(f"business_analysis in final_state: {final_state.get('business_analysis') is not None}")
        if final_state.get('business_analysis'):
            logger.info(f"business_analysis keys: {list(final_state['business_analysis'].keys())}")
        # Build response
        response_data = {
            "query_id": query_id,
            "question": request.question,
            "sql_query": final_state.get("sql_query"),
            "results": final_state.get("query_result") or final_state.get("results"),
            "insights": final_state.get("insights", []),
            "suggestions": final_state.get("suggestions", []),
            "formatted_response": final_state.get("formatted_response") or (
                final_state.get("messages", [""])[-1] if final_state.get("messages") else ""
            ),
            "error": final_state.get("error"),
            "metadata": {
                "pipeline": "langgraph",
                "execution_time": final_state.get("execution_time", 0),
                "tokens_used": final_state.get("tokens_used", 0),
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Extract business_analysis and visualization_data from business_analyst
        business_analysis = final_state.get("business_analysis") or None
        if business_analysis:
            # Include business_analysis in response for debugging/transparency
            response_data["business_analysis"] = business_analysis
            
            # Use frontend_display directly from LLM (elegant solution!)
            if business_analysis.get("frontend_display"):
                response_data["frontend_display"] = business_analysis["frontend_display"]
            else:
                # SEM FALLBACK - LLM deve gerar frontend_display obrigatoriamente
                logger.error(f"❌ LLM não gerou frontend_display para query {query_id}")
                raise ValueError("LLM deve gerar frontend_display obrigatoriamente. Verifique o prompt do business_analyst.")
            
            # Add insights and suggestions from business_analysis
            response_data["insights"] = business_analysis.get("key_findings", [])
            response_data["suggestions"] = business_analysis.get("next_actions", [])
            
        # Add visualization_data if available
        if final_state.get("visualization_data"):
            response_data["visualization_data"] = final_state["visualization_data"]
        
        return AskResponse(**response_data)
        
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}", exc_info=True)
        return AskResponse(
            query_id=str(uuid.uuid4()),
            question=request.question,
            sql_query=None,
            results=None,
            insights=[],
            suggestions=[],
            formatted_response=None,
            error=str(e),
            metadata={
                "pipeline": "langgraph",
                "error_type": type(e).__name__,
                "timestamp": datetime.now().isoformat()
            }
        )


@app.post("/feedback/simple", response_model=FeedbackResponse)
async def submit_simple_feedback(request: SimpleFeedbackRequest):
    """Submit simple positive/negative feedback."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # Save to PostgreSQL using ORM
        feedback_id = None
        with db_manager.get_session() as session:
            explanation = ""
            if request.feedback_type == "negative":
                explanation = f"Categoria: {request.category or 'Não especificada'}"
                if request.comment:
                    explanation += f". Comentário: {request.comment}"
            elif request.feedback_type == "positive":
                explanation = "Feedback positivo - resposta satisfatória"
                
            pg_feedback = PGFeedbackCorrection(
                question=request.original_question,
                original_query="",
                corrected_query="",
                explanation=explanation,
                patterns_extracted=[],
                meta_data={
                    "feedback_type": request.feedback_type,
                    "query_id": request.query_id,
                    "category": request.category or "",
                    "comment": request.comment or "",
                    "client_id": request.client_id,
                    "sector": request.sector,
                    "user_id": request.user_id or "anonymous"
                }
            )
            session.add(pg_feedback)
            session.flush()
            
            # Update related cache query feedback metrics if we can find it
            from src.models.learning_models import QueryCache
            cache_query = session.execute(text("""
                SELECT id FROM query_cache 
                WHERE question ILIKE :question_pattern
                ORDER BY confidence DESC, use_count DESC
                LIMIT 1
            """), {"question_pattern": f"%{request.original_question[:50]}%"}).fetchone()
            
            if cache_query:
                cache_id = cache_query[0]
                # Link feedback to cache
                pg_feedback.query_cache_id = cache_id
                
                # Update feedback metrics in cache
                if request.feedback_type == "positive":
                    session.execute(text("""
                        UPDATE query_cache 
                        SET positive_feedback_count = COALESCE(positive_feedback_count, 0) + 1,
                            last_feedback_date = CURRENT_TIMESTAMP,
                            feedback_score = CASE 
                                WHEN feedback_score IS NULL THEN 0.2
                                ELSE LEAST(1.0, feedback_score + 0.2)
                            END
                        WHERE id = :cache_id
                    """), {"cache_id": cache_id})
                elif request.feedback_type == "negative":
                    session.execute(text("""
                        UPDATE query_cache 
                        SET negative_feedback_count = COALESCE(negative_feedback_count, 0) + 1,
                            last_feedback_date = CURRENT_TIMESTAMP,
                            feedback_score = CASE 
                                WHEN feedback_score IS NULL THEN -0.3
                                ELSE GREATEST(-1.0, feedback_score - 0.3)
                            END
                        WHERE id = :cache_id
                    """), {"cache_id": cache_id})
                    
                logger.info(f"✅ Updated cache feedback metrics for query {cache_id}")
            
            session.commit()
            feedback_id = str(pg_feedback.id)
        
        logger.info(f"✅ Simple feedback saved: {feedback_id} ({request.feedback_type})")
        
        # Update learning patterns based on feedback type
        patterns_found = []
        if request.feedback_type == "positive":
            patterns_found.append("positive_confirmation")
        elif request.feedback_type == "negative":
            if request.category:
                patterns_found.append(f"negative_{request.category}")
        
        return FeedbackResponse(
            feedback_id=feedback_id,
            status="accepted",
            message=f"Feedback {request.feedback_type} registrado com sucesso",
            patterns_found=patterns_found,
            learning_updated=True
        )
        
    except Exception as e:
        logger.error(f"Error processing simple feedback: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback: {str(e)}")


@app.post("/feedback", response_model=FeedbackResponse)
async def submit_feedback(request: FeedbackRequest):
    """Submit feedback to improve the system."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # Create feedback model
        feedback = create_feedback_from_natural_language(
            feedback_text=request.feedback_text,
            query_id=request.query_id,
            original_question=request.original_question,
            client_id=request.client_id,
            sector=request.sector,
            source=FeedbackSource.API,
            user_id=request.user_id
        )
        
        if request.corrected_sql:
            feedback.corrected_sql = request.corrected_sql
        
        # Save to PostgreSQL
        feedback_id = None
        with db_manager.get_session() as session:
            pg_feedback = PGFeedbackCorrection(
                question=feedback.original_question,
                original_query=feedback.original_sql or "",
                corrected_query=feedback.corrected_sql or "",
                explanation=feedback.feedback_text,
                patterns_extracted=[],
                meta_data={
                    "feedback_type": feedback.feedback_type.value,
                    "source": feedback.source.value,
                    "client_id": feedback.client_id,
                    "sector": feedback.sector,
                    "user_id": feedback.user_id,
                    "query_id": feedback.query_id
                }
            )
            session.add(pg_feedback)
            session.flush()
            feedback_id = str(pg_feedback.id)
        
        logger.info(f"✅ Feedback saved: {feedback_id}")
        
        # Extract patterns
        patterns_found = []
        feedback_lower = request.feedback_text.lower()
        if "should use" in feedback_lower or "deveria usar" in feedback_lower:
            patterns_found.append("column_preference")
        if "filter" in feedback_lower or "filtro" in feedback_lower:
            patterns_found.append("filtering_logic")
        
        return FeedbackResponse(
            feedback_id=feedback_id,
            status="accepted",
            message="Feedback received and will improve future queries",
            patterns_found=patterns_found,
            learning_applied=True,
            cache_updated=bool(request.corrected_sql)
        )
        
    except Exception as e:
        logger.error(f"Error processing feedback: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback: {str(e)}")


@app.post("/feedback/reprocess", response_model=AskResponse)
async def feedback_and_reprocess(request: FeedbackReprocessRequest):
    """Submit feedback and immediately reprocess with intelligent corrections."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # 1. Submit feedback first (save to database and update cache metrics)
        logger.info(f"📝 Processing feedback for reprocessing: {request.feedback.feedback_type}")
        feedback_response = await submit_simple_feedback(request.feedback)
        
        if not request.reprocess:
            # Just return feedback response if no reprocessing requested
            return AskResponse(
                query_id=request.feedback.query_id,
                question=request.feedback.original_question,
                error=None,
                metadata={"feedback_only": True, "feedback_id": feedback_response.feedback_id}
            )
        
        # 2. Build feedback context for intelligent reprocessing
        feedback_context = None
        with db_manager.get_session() as session:
            # Get the feedback we just saved
            query = text("""
                SELECT question, original_query, corrected_query, explanation, meta_data
                FROM feedback_corrections 
                WHERE id = :feedback_id
            """)
            result = session.execute(query, {"feedback_id": feedback_response.feedback_id}).fetchone()
            
            if result:
                feedback_context = {
                    "question": result[0],
                    "original_query": result[1] or "",
                    "corrected_query": result[2] or "",
                    "explanation": result[3] or "",
                    "metadata": result[4] or {}
                }
                logger.info(f"✅ Feedback context prepared for reprocessing")
        
        # 3. Build state for reprocessing with feedback context
        reprocess_request = AskRequest(
            question=request.feedback.original_question,
            client_id=request.feedback.client_id,
            sector=request.feedback.sector,
            channel="api_reprocess",
            session_id=f"reprocess_{request.feedback.query_id}"
        )
        
        state = build_initial_state(reprocess_request)
        state["query_id"] = f"RPR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        state["original_query_id"] = request.feedback.query_id
        state["feedback_context"] = feedback_context  # Key: inject feedback for analysis
        state["reprocessing_mode"] = True
        state["feedback_id"] = feedback_response.feedback_id
        
        # 4. Execute pipeline with feedback-aware improvements
        logger.info(f"🔄 Reprocessing with feedback context: {request.feedback.feedback_type}")
        final_state = graph.invoke(state, config={"thread_id": state["thread_id"]})
        
        # 5. Build enhanced response
        response_data = {
            "query_id": state["query_id"],
            "question": request.feedback.original_question,
            "sql_query": final_state.get("sql_query"),
            "results": final_state.get("query_result") or final_state.get("results"),
            "insights": final_state.get("insights", []),
            "suggestions": final_state.get("suggestions", []),
            "formatted_response": final_state.get("formatted_response") or (
                final_state.get("messages", [""])[-1] if final_state.get("messages") else ""
            ),
            "error": final_state.get("error"),
            "metadata": {
                "pipeline": "langgraph_reprocess",
                "original_query_id": request.feedback.query_id,
                "feedback_id": feedback_response.feedback_id,
                "feedback_type": request.feedback.feedback_type,
                "feedback_category": request.feedback.category,
                "reprocessing_applied": True,
                "correction_insights": final_state.get("correction_insights", {}),
                "feedback_analyzed": final_state.get("feedback_analyzed", False),
                "execution_time": final_state.get("execution_time", 0),
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Add business analysis and visualization data if available
        business_analysis = final_state.get("business_analysis")
        if business_analysis:
            response_data["business_analysis"] = business_analysis
            if business_analysis.get("frontend_display"):
                response_data["frontend_display"] = business_analysis["frontend_display"]
            response_data["insights"] = business_analysis.get("key_findings", [])
            response_data["suggestions"] = business_analysis.get("next_actions", [])
        
        if final_state.get("visualization_data"):
            response_data["visualization_data"] = final_state["visualization_data"]
        
        logger.info(f"✅ Reprocessing completed successfully with feedback improvements")
        return AskResponse(**response_data)
        
    except Exception as e:
        logger.error(f"❌ Error in feedback reprocessing: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error in feedback reprocessing: {str(e)}")


@app.post("/reprocess")
async def reprocess_with_feedback(request: ReprocessRequest):
    """Reprocess a question with learned corrections."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # Get recent feedback if requested
        feedback_context = None
        if request.apply_recent_feedback:
            with db_manager.get_session() as session:
                query = text("""
                    SELECT question, original_query, corrected_query, explanation, meta_data
                    FROM feedback_corrections 
                    WHERE question ILIKE :question
                    ORDER BY created_at DESC 
                    LIMIT 1
                """)
                result = session.execute(query, {"question": f"%{request.original_question[:50]}%"}).fetchone()
                
                if result:
                    feedback_context = {
                        "question": result[0],
                        "original_query": result[1],
                        "corrected_query": result[2],
                        "explanation": result[3],
                        "metadata": result[4]
                    }
        
        # Build state with feedback context
        state = build_initial_state(AskRequest(
            question=request.original_question,
            client_id=request.client_id,
            sector=request.sector,
            channel="api"
        ))
        state["feedback_context"] = feedback_context
        state["query_id"] = f"RPR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # Execute pipeline
        final_state = graph.invoke(state, config={"thread_id": state["thread_id"]})
        
        # Return same format as /ask
        return await ask(AskRequest(
            question=request.original_question,
            client_id=request.client_id,
            sector=request.sector,
            channel="api"
        ))
        
    except Exception as e:
        logger.error(f"Error in reprocessing: {e}")
        raise HTTPException(status_code=500, detail=f"Error reprocessing: {str(e)}")


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """System health check with mandatory Nível 2 verification."""
    # Check PostgreSQL
    pg_status = "unavailable"
    if LEARNING_SYSTEMS_AVAILABLE and db_manager:
        try:
            health = db_manager.health_check()
            pg_status = health["status"]
        except:
            pg_status = "error"
    
    # 🚀 VERIFICAÇÃO OBRIGATÓRIA: Nível 2 para performance adequada
    from pathlib import Path
    nivel2_status = "disabled"
    nivel2_path = Path("src/config/setores/cambio/L2M/nivel2")
    nivel2_required_files = [
        nivel2_path / "patterns.json",
        nivel2_path / "domain_config.json", 
        nivel2_path / "column_aliases.json"
    ]
    
    if nivel2_path.exists() and all(f.exists() for f in nivel2_required_files):
        nivel2_status = "active"
    
    # Sistema deve estar degraded se Nível 2 não estiver ativo
    overall_status = "healthy"
    if pg_status != "healthy":
        overall_status = "degraded"
    elif nivel2_status != "active":
        overall_status = "degraded"
    
    return HealthResponse(
        status=overall_status,
        database={
            "postgresql": pg_status,
            "learning_available": LEARNING_SYSTEMS_AVAILABLE
        },
        systems={
            "pipeline": "operational",
            "learning": "operational" if LEARNING_SYSTEMS_AVAILABLE else "limited",
            "optimization_status": nivel2_status,
            "workflow_type": "optimized_parallel",
            "level2_enabled": nivel2_status == "active"
        },
        feature_flags={
            "learning_enabled": LEARNING_SYSTEMS_AVAILABLE,
            "feedback_enabled": True,
            "frontend_display": True,
            "nivel2_required": True
        }
    )


@app.get("/metrics")
async def get_metrics():
    """Get system metrics."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        return {"error": "Metrics unavailable - learning system not initialized"}
    
    try:
        stats = db_manager.get_feedback_statistics()
        return {
            "feedback": {
                "total": stats.get("feedback_corrections", {}).get("total", 0),
                "recent_7_days": stats.get("feedback_corrections", {}).get("recent_7_days", 0)
            },
            "patterns": {
                "total": stats.get("correction_patterns", {}).get("total", 0),
                "by_type": stats.get("correction_patterns", {}).get("by_type", [])
            },
            "cache": {
                "total_queries": stats.get("query_cache", {}).get("total", 0)
            },
            "generated_at": stats.get("generated_at", datetime.now().isoformat())
        }
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving metrics")


@app.get("/debug/env")
async def debug_env():
    """Debug endpoint to check environment variables."""
    import os
    from src.config.settings import load_settings

    settings = load_settings()

    return {
        "fireworks_api_key_os": "SET" if os.getenv('FIREWORKS_API_KEY') else "NOT_SET",
        "fireworks_api_key_settings": "SET" if settings.FIREWORKS_API_KEY else "NOT_SET",
        "anthropic_api_key_os": "SET" if os.getenv('ANTHROPIC_API_KEY') else "NOT_SET",
        "anthropic_api_key_settings": "SET" if settings.ANTHROPIC_API_KEY else "NOT_SET",
        "groq_api_key_os": "SET" if os.getenv('GROQ_API_KEY') else "NOT_SET",
        "groq_api_key_settings": "SET" if settings.GROQ_API_KEY else "NOT_SET",
        "datahero_llm_provider_os": os.getenv('DATAHERO_LLM_PROVIDER', 'NOT_SET'),
        "use_optimized_workflow_os": os.getenv('USE_OPTIMIZED_WORKFLOW', 'NOT_SET')
    }

@app.get("/")
async def root():
    """API information."""
    return {
        "name": "DataHero API",
        "version": "4.0.0",
        "description": "Unified DataHero API with LangGraph pipeline",
        "endpoints": {
            "/ask": "Process questions",
            "/feedback": "Submit feedback",
            "/feedback/simple": "Submit simple positive/negative feedback",
            "/feedback/reprocess": "Submit feedback and reprocess immediately",
            "/reprocess": "Reprocess with corrections",
            "/health": "System health",
            "/metrics": "System metrics",
            "/debug/env": "Debug environment variables",
            "/docs": "API documentation"
        }
    }


# Mount static files at the very end, after all API routes are defined
# This ensures API routes take precedence over static file serving
static_dir = "/app/static"
if os.path.exists(static_dir):
    # Mount static files at /static prefix to avoid conflicts
    app.mount("/static", StaticFiles(directory=static_dir), name="static")
    
    # Serve index.html for the root and any non-API routes (SPA support)
    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        # Skip API routes - they should 404 if not found
        if full_path.startswith(("ask", "feedback", "health", "metrics", "reprocess")):
            raise HTTPException(status_code=404, detail="API endpoint not found")
        
        # For the root path or any other path, serve index.html
        index_path = os.path.join(static_dir, "index.html")
        if os.path.exists(index_path):
            return FileResponse(index_path)
        
        # If index.html doesn't exist, return 404
        raise HTTPException(status_code=404, detail="Frontend not found")
    
    logger.info(f"✅ Frontend static files mounted from {static_dir}")
else:
    logger.info("⚠️ Frontend static files not found, serving API only")