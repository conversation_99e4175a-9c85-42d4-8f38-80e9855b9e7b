"""
Learning Database Utils for PostgreSQL - DataHero4
================================================

Database utilities for managing PostgreSQL connections, sessions, and operations
for the learning systems (feedback store, query cache, pattern learning).

Features:
- Connection pooling with SQLAlchemy
- Session management with context managers
- Migration utilities from SQLite to PostgreSQL
- Health checks and monitoring
- Batch operations for performance
"""

import os
import logging
import sqlite3
import pickle
import json
from typing import Dict, List, Any, Optional, Union, Generator
from contextlib import contextmanager
from datetime import datetime

import psycopg2
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError

from ..models.learning_models import (
    Base, FeedbackCorrection, CorrectionPattern, QueryCache,
    PatternApplication, QueryHistory, QueryEmbedding,
    create_all_tables, get_table_names
)

logger = logging.getLogger(__name__)


class LearningDBManager:
    """
    PostgreSQL database manager for learning systems.
    Handles connections, sessions, and operations for all learning tables.
    """
    
    def __init__(self, 
                 host: str = None,
                 port: int = None,
                 database: str = None,
                 user: str = None,
                 password: str = None,
                 pool_size: int = 10,
                 max_overflow: int = 20,
                 pool_timeout: int = 30,
                 pool_recycle: int = 3600):
        """
        Initialize database manager with connection pooling for Learning database.
        
        Args:
            host: PostgreSQL host (defaults to DB_LEARNING_HOST)
            port: PostgreSQL port (defaults to 40846 for learning DB)
            database: Database name (defaults to DB_LEARNING_NAME)
            user: Database user (defaults to DB_LEARNING_USER)
            password: Database password (defaults to DB_LEARNING_PASSWORD)
            pool_size: Connection pool size
            max_overflow: Max overflow connections
            pool_timeout: Pool timeout in seconds
            pool_recycle: Pool recycle time in seconds
        """
        # Use learning database environment variables by default
        # Try to import settings, fallback to environment variables
        try:
            from src.config.settings import load_settings
            settings = load_settings()
            self.host = host or settings.DB_LEARNING_HOST
            self.port = port or settings.DB_LEARNING_PORT
            self.database = database or settings.DB_LEARNING_NAME
            self.user = user or settings.DB_LEARNING_USER
            self.password = password or settings.DB_LEARNING_PASSWORD
        except ImportError:
            # Fallback to direct environment variables
            self.host = host or os.getenv('DB_LEARNING_HOST', 'switchyard.proxy.rlwy.net')
            self.port = port or int(os.getenv('DB_LEARNING_PORT', '51728'))
            self.database = database or os.getenv('DB_LEARNING_NAME', 'railway')
            self.user = user or os.getenv('DB_LEARNING_USER', 'postgres')
            self.password = password or os.getenv('DB_LEARNING_PASSWORD')
        
        if not self.password:
            raise ValueError("Learning database password not provided. Set DB_LEARNING_PASSWORD environment variable.")
        
        # Create connection string
        self.connection_string = (
            f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
        )
        
        # Create engine with connection pooling
        self.engine = create_engine(
            self.connection_string,
            poolclass=QueuePool,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=pool_timeout,
            pool_recycle=pool_recycle,
            echo=False  # Set to True for SQL debugging
        )
        
        # Create session factory
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Initialize tables
        self._init_tables()
        
        logger.info(f"LearningDBManager initialized for {self.host}/{self.database}")
    
    def _init_tables(self):
        """Initialize all learning tables if they don't exist."""
        try:
            create_all_tables(self.engine)
            logger.info("Learning tables initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing tables: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Context manager for database sessions.
        Automatically handles commit/rollback and session cleanup.
        
        Usage:
            with db_manager.get_session() as session:
                # database operations
                pass
        """
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the database connection.
        
        Returns:
            Dict with health status and metrics
        """
        try:
            with self.get_session() as session:
                # Test basic connectivity
                result = session.execute(text("SELECT 1")).scalar()
                
                # Get table counts
                table_counts = {}
                for table_name in get_table_names():
                    count = session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
                    table_counts[table_name] = count
                
                # Get connection pool stats
                pool = self.engine.pool
                pool_stats = {
                    "pool_size": pool.size(),
                    "checked_in": pool.checkedin(),
                    "checked_out": pool.checkedout(),
                    "overflow": pool.overflow()
                }
                
                return {
                    "status": "healthy",
                    "connected": True,
                    "table_counts": table_counts,
                    "pool_stats": pool_stats,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def migrate_from_sqlite(self, sqlite_paths: Dict[str, str]) -> Dict[str, Any]:
        """
        Migrate data from SQLite databases to PostgreSQL.
        
        Args:
            sqlite_paths: Dict mapping table types to SQLite file paths
                Example: {
                    'feedback_store': 'data/feedback_store.db',
                    'query_cache': 'data/query_cache.db', 
                    'feedback_history': 'data/feedback_history.db'
                }
        
        Returns:
            Migration results with counts and errors
        """
        migration_results = {
            "started_at": datetime.now().isoformat(),
            "tables_migrated": {},
            "errors": [],
            "total_records": 0
        }
        
        try:
            with self.get_session() as session:
                
                # Migrate feedback_store.db
                if 'feedback_store' in sqlite_paths:
                    fb_results = self._migrate_feedback_store(session, sqlite_paths['feedback_store'])
                    migration_results["tables_migrated"]["feedback_store"] = fb_results
                    migration_results["total_records"] += fb_results.get("total_records", 0)
                
                # Migrate query_cache.db  
                if 'query_cache' in sqlite_paths:
                    cache_results = self._migrate_query_cache(session, sqlite_paths['query_cache'])
                    migration_results["tables_migrated"]["query_cache"] = cache_results
                    migration_results["total_records"] += cache_results.get("total_records", 0)
                
                # Migrate feedback_history.db
                if 'feedback_history' in sqlite_paths:
                    history_results = self._migrate_feedback_history(session, sqlite_paths['feedback_history'])
                    migration_results["tables_migrated"]["feedback_history"] = history_results  
                    migration_results["total_records"] += history_results.get("total_records", 0)
                
                migration_results["completed_at"] = datetime.now().isoformat()
                migration_results["status"] = "success"
                
                logger.info(f"Migration completed successfully. Total records: {migration_results['total_records']}")
                
        except Exception as e:
            migration_results["status"] = "failed"
            migration_results["error"] = str(e)
            migration_results["completed_at"] = datetime.now().isoformat()
            logger.error(f"Migration failed: {e}")
        
        return migration_results
    
    def _migrate_feedback_store(self, session: Session, sqlite_path: str) -> Dict[str, Any]:
        """Migrate feedback_store.db to PostgreSQL."""
        results = {"feedback_corrections": 0, "correction_patterns": 0, "total_records": 0}
        
        if not os.path.exists(sqlite_path):
            logger.warning(f"SQLite file not found: {sqlite_path}")
            return results
        
        try:
            sqlite_conn = sqlite3.connect(sqlite_path)
            sqlite_cursor = sqlite_conn.cursor()
            
            # Migrate feedback_corrections
            sqlite_cursor.execute("SELECT * FROM feedback_corrections")
            for row in sqlite_cursor.fetchall():
                feedback = FeedbackCorrection(
                    id=row[0],
                    question=row[1],
                    original_query=row[2], 
                    corrected_query=row[3],
                    explanation=row[4],
                    patterns_extracted=json.loads(row[5]) if row[5] else [],
                    created_at=datetime.fromisoformat(row[6]),
                    applied_count=row[7] or 0,
                    success_rate=row[8] or 0.0,
                    meta_data=json.loads(row[9]) if row[9] else {}
                )
                session.merge(feedback)  # Use merge to handle duplicates
                results["feedback_corrections"] += 1
            
            # Migrate correction_patterns
            sqlite_cursor.execute("SELECT * FROM correction_patterns") 
            for row in sqlite_cursor.fetchall():
                pattern = CorrectionPattern(
                    id=row[0],
                    pattern_type=row[1],
                    pattern_regex=row[2],
                    replacement_template=row[3],
                    confidence=row[4],
                    examples=json.loads(row[5]) if row[5] else [],
                    created_at=datetime.fromisoformat(row[6]),
                    use_count=row[7] or 0,
                    success_count=row[8] or 0
                )
                session.merge(pattern)
                results["correction_patterns"] += 1
            
            sqlite_conn.close()
            results["total_records"] = results["feedback_corrections"] + results["correction_patterns"]
            
        except Exception as e:
            logger.error(f"Error migrating feedback_store: {e}")
            raise
        
        return results
    
    def _migrate_query_cache(self, session: Session, sqlite_path: str) -> Dict[str, Any]:
        """Migrate query_cache.db to PostgreSQL."""
        results = {"query_cache": 0, "total_records": 0}
        
        if not os.path.exists(sqlite_path):
            logger.warning(f"SQLite file not found: {sqlite_path}")
            return results
        
        try:
            sqlite_conn = sqlite3.connect(sqlite_path)
            sqlite_cursor = sqlite_conn.cursor()
            
            # Migrate query_cache
            sqlite_cursor.execute("SELECT * FROM query_cache")
            for row in sqlite_cursor.fetchall():
                # Handle embedding - stored as JSON string in SQLite
                embedding_data = None
                if row[3]:  # embedding column
                    try:
                        # Try to parse as JSON first, then as pickle
                        embedding_list = json.loads(row[3])
                        embedding_data = pickle.dumps(embedding_list)
                    except (json.JSONDecodeError, TypeError):
                        # Assume it's already pickled
                        embedding_data = row[3]
                
                cache_entry = QueryCache(
                    id=row[0],
                    question=row[1],
                    sql_query=row[2],
                    embedding=embedding_data,
                    entities=json.loads(row[4]) if row[4] else {},
                    confidence=row[5],
                    execution_time=row[6],
                    result_count=row[7],
                    created_at=datetime.fromisoformat(row[8]),
                    last_used=datetime.fromisoformat(row[9]),
                    use_count=row[10] or 1,
                    source=row[11] or 'llm',
                    meta_data=json.loads(row[12]) if row[12] else {}
                )
                session.merge(cache_entry)
                results["query_cache"] += 1
            
            sqlite_conn.close()
            results["total_records"] = results["query_cache"]
            
        except Exception as e:
            logger.error(f"Error migrating query_cache: {e}")
            raise
        
        return results
    
    def _migrate_feedback_history(self, session: Session, sqlite_path: str) -> Dict[str, Any]:
        """Migrate feedback_history.db to PostgreSQL."""
        results = {"query_history": 0, "total_records": 0}
        
        if not os.path.exists(sqlite_path):
            logger.warning(f"SQLite file not found: {sqlite_path}")
            return results
        
        try:
            sqlite_conn = sqlite3.connect(sqlite_path)
            sqlite_cursor = sqlite_conn.cursor()
            
            # Migrate query_history
            sqlite_cursor.execute("SELECT * FROM query_history")
            for row in sqlite_cursor.fetchall():
                history = QueryHistory(
                    id=row[0],
                    timestamp=datetime.fromisoformat(row[1]),
                    question=row[2],
                    sql_query=row[3],
                    has_feedback=bool(row[4]) if row[4] is not None else False,
                    feedback_type=row[5],
                    feedback_explanation=row[6],
                    corrected_query=row[7]
                )
                session.merge(history)
                results["query_history"] += 1
            
            sqlite_conn.close()
            results["total_records"] = results["query_history"]
            
        except Exception as e:
            logger.error(f"Error migrating feedback_history: {e}")
            raise
        
        return results
    
    def get_feedback_statistics(self) -> Dict[str, Any]:
        """Get comprehensive feedback statistics."""
        try:
            with self.get_session() as session:
                stats = {}
                
                # Feedback corrections stats
                total_feedback = session.query(FeedbackCorrection).count()
                avg_success_rate = session.execute(
                    text("SELECT AVG(success_rate) FROM feedback_corrections")
                ).scalar() or 0.0
                
                # Recent feedback (last 7 days)
                recent_feedback = session.execute(text("""
                    SELECT COUNT(*) FROM feedback_corrections 
                    WHERE created_at >= NOW() - INTERVAL '7 days'
                """)).scalar() or 0
                
                # Pattern stats
                total_patterns = session.query(CorrectionPattern).count()
                pattern_types = session.execute(text("""
                    SELECT pattern_type, COUNT(*) as count, AVG(confidence) as avg_confidence
                    FROM correction_patterns 
                    GROUP BY pattern_type 
                    ORDER BY count DESC
                """)).fetchall()
                
                # Cache stats
                total_cached_queries = session.query(QueryCache).count()
                cache_sources = session.execute(text("""
                    SELECT source, COUNT(*) as count
                    FROM query_cache 
                    GROUP BY source
                """)).fetchall()
                
                stats = {
                    "feedback_corrections": {
                        "total": total_feedback,
                        "recent_7_days": recent_feedback,
                        "avg_success_rate": float(avg_success_rate)
                    },
                    "correction_patterns": {
                        "total": total_patterns,
                        "by_type": [{"type": row[0], "count": row[1], "avg_confidence": float(row[2])} 
                                   for row in pattern_types]
                    },
                    "query_cache": {
                        "total": total_cached_queries,
                        "by_source": [{"source": row[0], "count": row[1]} for row in cache_sources]
                    },
                    "generated_at": datetime.now().isoformat()
                }
                
                return stats
                
        except Exception as e:
            logger.error(f"Error getting feedback statistics: {e}")
            return {"error": str(e)}
    
    def cleanup_old_data(self, days: int = 90) -> Dict[str, int]:
        """
        Clean up old data from learning tables.
        
        Args:
            days: Number of days to keep data
            
        Returns:
            Dictionary with cleanup counts
        """
        try:
            with self.get_session() as session:
                cutoff_date = datetime.now().timestamp() - (days * 24 * 60 * 60)
                cutoff_datetime = datetime.fromtimestamp(cutoff_date)
                
                cleanup_counts = {}
                
                # Clean old query cache entries
                deleted_cache = session.execute(text("""
                    DELETE FROM query_cache 
                    WHERE created_at < :cutoff_date AND use_count <= 1
                """), {"cutoff_date": cutoff_datetime}).rowcount
                cleanup_counts["query_cache"] = deleted_cache
                
                # Clean old query history
                deleted_history = session.execute(text("""
                    DELETE FROM query_history 
                    WHERE timestamp < :cutoff_date AND has_feedback = false
                """), {"cutoff_date": cutoff_datetime}).rowcount
                cleanup_counts["query_history"] = deleted_history
                
                # Clean old pattern applications
                deleted_apps = session.execute(text("""
                    DELETE FROM pattern_applications 
                    WHERE applied_at < :cutoff_date
                """), {"cutoff_date": cutoff_datetime}).rowcount
                cleanup_counts["pattern_applications"] = deleted_apps
                
                logger.info(f"Cleanup completed: {cleanup_counts}")
                return cleanup_counts
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return {"error": str(e)}
    
    def close(self):
        """Close database connections and cleanup resources."""
        try:
            self.engine.dispose()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error closing database connections: {e}")


# Global instance (initialized when needed)
_db_manager: Optional[LearningDBManager] = None


def get_db_manager() -> LearningDBManager:
    """Get or create global database manager instance."""
    global _db_manager
    if _db_manager is None:
        _db_manager = LearningDBManager()
    return _db_manager


def init_learning_db(host: str = None, port: int = 5432, database: str = None, 
                    user: str = None, password: str = None) -> LearningDBManager:
    """
    Initialize learning database with custom parameters.
    
    Args:
        host: PostgreSQL host
        port: PostgreSQL port
        database: Database name
        user: Database user
        password: Database password
        
    Returns:
        Initialized database manager
    """
    global _db_manager
    _db_manager = LearningDBManager(
        host=host, port=port, database=database,
        user=user, password=password
    )
    return _db_manager


def migrate_all_sqlite_data(sqlite_dir: str = "data") -> Dict[str, Any]:
    """
    Convenience function to migrate all SQLite data to PostgreSQL.
    
    Args:
        sqlite_dir: Directory containing SQLite files
        
    Returns:
        Migration results
    """
    db_manager = get_db_manager()
    
    sqlite_paths = {
        'feedback_store': os.path.join(sqlite_dir, 'feedback_store.db'),
        'query_cache': os.path.join(sqlite_dir, 'query_cache.db'),
        'feedback_history': os.path.join(sqlite_dir, 'feedback_history.db')
    }
    
    return db_manager.migrate_from_sqlite(sqlite_paths) 