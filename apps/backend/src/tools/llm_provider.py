from typing import Any, Dict
import yaml
import os
import time
import random
from functools import wraps
# Placeholder para a biblioteca Together AI
try:
    import together
except ImportError:
    together = None

# Biblioteca Anthropic
try:
    import anthropic
except ImportError:
    anthropic = None

# Biblioteca Google Gemini
try:
    from langchain_google_genai import ChatGoogleGenerativeAI
except ImportError:
    ChatGoogleGenerativeAI = None

# Biblioteca Fireworks AI - Use OpenAI client with Fireworks base URL
try:
    from openai import OpenAI
    Fireworks = OpenAI  # Use OpenAI client for Fireworks compatibility
except ImportError:
    Fireworks = None

def retry_with_exponential_backoff(
    max_retries=3,
    base_delay=1,
    max_delay=60,
    exponential_base=2,
    jitter=True,
):
    """Decorator que implementa retry com backoff exponencial para lidar com rate limits."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            num_retries = 0
            delay = base_delay
            
            while num_retries <= max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # Verifica se é erro de rate limit
                    error_msg = str(e).lower()
                    is_rate_limit = any(term in error_msg for term in [
                        'rate limit', 'rate_limit', 'too many requests', '429',
                        'quota exceeded', 'throttled'
                    ])
                    
                    num_retries += 1
                    
                    if num_retries > max_retries:
                        print(f"ERRO: Máximo de tentativas ({max_retries}) excedido.")
                        raise
                    
                    if is_rate_limit:
                        # Calcula delay com backoff exponencial
                        delay = min(delay * (exponential_base ** (num_retries - 1)), max_delay)
                        
                        # Adiciona jitter para evitar thundering herd
                        if jitter:
                            delay = delay * (0.5 + random.random())
                        
                        print(f"RATE LIMIT detectado. Aguardando {delay:.2f}s antes da tentativa {num_retries}/{max_retries}...")
                        time.sleep(delay)
                    else:
                        # Para outros erros, re-lança imediatamente
                        raise
                        
            return None
        return wrapper
    return decorator

# Rate limiter global simples
class SimpleRateLimiter:
    def __init__(self, calls_per_minute=30):
        self.calls_per_minute = calls_per_minute
        self.min_interval = 60.0 / calls_per_minute
        self.last_call_time = 0
    
    def wait_if_needed(self):
        """Aguarda se necessário para respeitar o rate limit."""
        current_time = time.time()
        time_since_last_call = current_time - self.last_call_time
        
        if time_since_last_call < self.min_interval:
            sleep_time = self.min_interval - time_since_last_call
            print(f"Rate limiter: aguardando {sleep_time:.2f}s para respeitar limite de {self.calls_per_minute} chamadas/min")
            time.sleep(sleep_time)
        
        self.last_call_time = time.time()

# Instância global do rate limiter
# Configurado para Fireworks AI com rate limits generosos (600 requests/minute)
_rate_limiter = SimpleRateLimiter(calls_per_minute=500)

class LLMProvider:
    """
    Provedor de LLM parametrizado por cliente/setor, suporta múltiplos backends.
    """
    def __init__(self, setor: str, cliente: str, agent_name: str, base_path: str = "src/config/setores"):
        config_path = f"{base_path}/{setor}/{cliente}/llm.yaml"
        with open(config_path, "r") as f:
            full_config = yaml.safe_load(f)
        self.agent_config = full_config.get(agent_name)
        if not self.agent_config:
            raise ValueError(f"Configuração não encontrada para agente {agent_name} em {config_path}")
        self.provider = self.agent_config.get("provider", "openai")
        self.model = self.agent_config.get("model")
        self.temperature = self.agent_config.get("temperature")
        # A API key será carregada pela biblioteca do provider (ex: together)
        # diretamente da variável de ambiente padrão (ex: TOGETHER_API_KEY)
        # Não é necessário verificar aqui, a biblioteca fará isso.

    def invoke(self, prompt: str, **kwargs) -> str:
        """
        Gera resposta do LLM configurado. (Interface compatível com agentes).

        Args:
            prompt (str): Prompt para o LLM.
            **kwargs: Parâmetros extras.

        Returns:
            str: Resposta do LLM.
        """
        # Aplica rate limiting global antes de qualquer chamada
        _rate_limiter.wait_if_needed()
        
        if self.provider == "openai":
            return self._call_openai(prompt, **kwargs)
        elif self.provider == "togetherai":
            return self._call_togetherai(prompt, **kwargs)
        elif self.provider == "anthropic":
            return self._call_anthropic(prompt, **kwargs)
        elif self.provider == "google":
            return self._call_google_gemini(prompt, **kwargs)
        elif self.provider == "fireworks":
            return self._call_fireworks(prompt, **kwargs)
        # Adicione outros provedores conforme necessário
        raise NotImplementedError(f"Provider {self.provider} não implementado.")

    def _call_openai(self, prompt: str, **kwargs) -> str:
        # Exemplo de chamada (mock)
        # Implemente integração real conforme necessário
        return f"[MOCK OPENAI] {prompt[:40]}..."

    @retry_with_exponential_backoff(max_retries=3, base_delay=2)
    def _call_anthropic(self, prompt: str, **kwargs) -> str:
        if not anthropic:
            print("ERROR: Biblioteca 'anthropic' não instalada. Instale com 'pip install anthropic'")
            raise ImportError("Biblioteca 'anthropic' não instalada.")
        
        # Extract context and timeout from kwargs
        context = kwargs.get('context', {})
        timeout_seconds = context.get('timeout', 60) # Default 60s if not provided
        
        # Get max_tokens from agent config, default to 1024 if not set
        max_tokens_config = self.agent_config.get('max_tokens', 1024) 

        try:
            print(f"INFO: Chamando Anthropic Claude com modelo {self.model}, timeout={timeout_seconds}s, max_tokens={max_tokens_config}")

            # Get API key from environment using settings
            from src.config.settings import load_settings
            settings = load_settings()
            api_key = settings.ANTHROPIC_API_KEY

            if not api_key:
                # Fallback to os.getenv for backward compatibility
                api_key = os.getenv('ANTHROPIC_API_KEY')
                if not api_key:
                    raise ValueError("ANTHROPIC_API_KEY não encontrada nas variáveis de ambiente")

            client = anthropic.Anthropic(api_key=api_key)

            response = client.messages.create(
                model=self.model,
                max_tokens=max_tokens_config,
                temperature=self.temperature,
                timeout=timeout_seconds,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            generated_text = response.content[0].text
            print(f"INFO: Resposta recebida do Anthropic Claude.")
            return generated_text
        except Exception as e:
            print(f"Erro ao chamar Anthropic Claude: {e}")
            raise RuntimeError(f"Erro na API Anthropic: {e}") from e

    @retry_with_exponential_backoff(max_retries=3, base_delay=2)
    def _call_togetherai(self, prompt: str, **kwargs) -> str:
        if not together:
            print("ERROR: Biblioteca 'together' não instalada. Instale com 'pip install together'")
            raise ImportError("Biblioteca 'together' não instalada.")
        
        # Extract context and timeout from kwargs
        context = kwargs.get('context', {})
        timeout_seconds = context.get('timeout', 60) # Default 60s if not provided
        
        # Get max_tokens from agent config, default to 1024 if not set
        max_tokens_config = self.agent_config.get('max_tokens', 1024) 

        try:
            print(f"INFO: Chamando Together.AI Chat Completions com modelo {self.model}, timeout={timeout_seconds}s, max_tokens={max_tokens_config}")

            # Get API key from environment using settings
            from src.config.settings import load_settings
            settings = load_settings()
            api_key = settings.TOGETHER_API_KEY

            if not api_key:
                # Fallback to os.getenv for backward compatibility
                api_key = os.getenv('TOGETHER_API_KEY')
                if not api_key:
                    raise ValueError("TOGETHER_API_KEY não encontrada nas variáveis de ambiente")

            client = together.Together(api_key=api_key)

            # Pass timeout and use configured max_tokens
            response = client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=max_tokens_config, # Use configured value
                # Assuming 'timeout' is the correct parameter name for the together library
                # You might need to verify this parameter name in the 'together' library docs.
                request_timeout=timeout_seconds
            )
            generated_text = response.choices[0].message.content
            print(f"INFO: Resposta recebida do Together.AI.")
            return generated_text
        except Exception as e:
            print(f"Erro ao chamar Together.AI Chat Completions: {e}")
            raise RuntimeError(f"Erro na API Together.AI: {e}") from e
    
    @retry_with_exponential_backoff(max_retries=3, base_delay=2)
    def _call_google_gemini(self, prompt: str, **kwargs) -> str:
        if not ChatGoogleGenerativeAI:
            print("ERROR: Biblioteca 'langchain-google-genai' não instalada. Instale com 'pip install langchain-google-genai'")
            raise ImportError("Biblioteca 'langchain-google-genai' não instalada.")
        
        # Extract context and timeout from kwargs
        context = kwargs.get('context', {})
        timeout_seconds = context.get('timeout', 60) # Default 60s if not provided
        
        # Get max_tokens from agent config, default to 1024 if not set
        max_tokens_config = self.agent_config.get('max_tokens', 1024) 
        
        try:
            print(f"INFO: Chamando Google Gemini com modelo {self.model}, timeout={timeout_seconds}s, max_tokens={max_tokens_config}")

            # Get API key from environment using settings
            from src.config.settings import load_settings
            settings = load_settings()
            api_key = settings.GOOGLE_API_KEY

            if not api_key:
                # Fallback to os.getenv for backward compatibility
                api_key = os.getenv('GOOGLE_API_KEY')
                if not api_key:
                    raise ValueError("GOOGLE_API_KEY não encontrada nas variáveis de ambiente")

            # Initialize Gemini model
            llm = ChatGoogleGenerativeAI(
                model=self.model,
                temperature=self.temperature,
                max_tokens=max_tokens_config,
                timeout=timeout_seconds,
                max_retries=2,
                google_api_key=api_key,
            )
            
            # Invoke the model
            response = llm.invoke(prompt)
            generated_text = response.content
            
            print(f"INFO: Resposta recebida do Google Gemini.")
            return generated_text
        except Exception as e:
            print(f"Erro ao chamar Google Gemini: {e}")
            raise RuntimeError(f"Erro na API Google Gemini: {e}") from e

    @retry_with_exponential_backoff(max_retries=3, base_delay=2)
    def _call_fireworks(self, prompt: str, **kwargs) -> str:
        """Chama Fireworks AI API usando OpenAI client."""
        if not Fireworks:
            print("ERROR: OpenAI library não instalada. Instale com 'pip install openai'")
            raise ImportError("OpenAI library não instalada.")

        # Extract context and timeout from kwargs
        context = kwargs.get('context', {})
        timeout_seconds = context.get('timeout', 60)

        # Get max_tokens from agent config, default to 1024 if not set
        max_tokens_config = self.agent_config.get('max_tokens', 1024)

        try:
            print(f"INFO: Chamando Fireworks AI com modelo {self.model}, timeout={timeout_seconds}s, max_tokens={max_tokens_config}")

            # Get API key from environment using settings
            from src.config.settings import load_settings
            settings = load_settings()
            api_key = settings.FIREWORKS_API_KEY

            if not api_key:
                # Fallback to os.getenv for backward compatibility
                api_key = os.getenv('FIREWORKS_API_KEY')
                if not api_key:
                    raise ValueError("FIREWORKS_API_KEY não encontrada nas variáveis de ambiente")

            # Initialize Fireworks client using OpenAI with Fireworks base URL
            client = Fireworks(
                api_key=api_key,
                base_url="https://api.fireworks.ai/inference/v1"
            )
            
            # Create chat completion
            response = client.chat.completions.create(
                model=self.model,
                messages=[{
                    "role": "user", 
                    "content": prompt
                }],
                temperature=self.temperature,
                max_tokens=max_tokens_config
            )
            
            generated_text = response.choices[0].message.content
            print(f"INFO: Resposta recebida do Fireworks AI.")
            return generated_text
            
        except Exception as e:
            print(f"Erro ao chamar Fireworks AI: {e}")
            raise RuntimeError(f"Erro na API Fireworks: {e}") from e