import os
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from typing import Any, Dict, List, Optional
import yaml


def load_db_config(setor: str, cliente: str, base_path: str = "src/config/setores") -> dict:
    """
    Carrega a configuração do banco de dados a partir do arquivo db_config.yaml do cliente/setor.

    Args:
        setor (str): Nome do setor.
        cliente (str): Nome do cliente.
        base_path (str): Caminho base para configs.

    Returns:
        dict: Configuração do banco de dados.
    """
    config_path = f"{base_path}/{setor}/{cliente}/db_config.yaml"
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    return config


def build_connection_string(db_config: dict) -> str:
    """
    Monta a string de conexão SQLAlchemy para PostgreSQL usando db_config e senha do .env.

    Args:
        db_config (dict): Configuração do banco de dados.

    Returns:
        str: String de conexão SQLAlchemy.
    """
    # Try to get password from settings first, then fallback to os.getenv
    password = None
    try:
        from src.config.settings import load_settings
        settings = load_settings()
        # Map common password aliases to settings attributes
        if db_config["password_alias"] == "L2M_DB_PASSWORD":
            password = getattr(settings, 'L2M_DB_PASSWORD', None)
    except (ImportError, AttributeError):
        pass

    # Fallback to os.getenv
    if not password:
        password = os.getenv(db_config["password_alias"])

    if not password:
        raise ValueError(f"Senha não encontrada para alias {db_config['password_alias']} no .env")
    return (
        f"postgresql://{db_config['user']}:{password}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    )


def get_engine(connection_string: str) -> Engine:
    """
    Cria um engine SQLAlchemy a partir da string de conexão.

    Args:
        connection_string (str): String de conexão SQLAlchemy.

    Returns:
        Engine: Engine SQLAlchemy.
    """
    return create_engine(connection_string)


def execute_query(engine: Engine, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Executa uma query SQL e retorna os resultados como lista de dicionários.

    Args:
        engine (Engine): Engine SQLAlchemy.
        query (str): Query SQL.
        params (dict, optional): Parâmetros para a query.

    Returns:
        list[dict]: Resultados da query.
    """
    with engine.connect() as conn:
        result = conn.execute(text(query), params or {})
        columns = result.keys()
        return [dict(zip(columns, row)) for row in result.fetchall()] 