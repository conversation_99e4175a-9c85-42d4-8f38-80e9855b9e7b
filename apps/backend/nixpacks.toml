[phases.setup]
nixPkgs = ["python310", "poetry", "gcc", "glibc", "stdenv.cc.cc.lib"]

[phases.install]
cmds = ["poetry install --only main --no-interaction --no-ansi"]

[variables]
LD_LIBRARY_PATH = "/nix/store/xpxln7rqi3pq4m0xpnawhxb2gs0mn1s0-gcc-12.3.0-lib/lib"

[start]
cmd = "LD_LIBRARY_PATH=/nix/store/xpxln7rqi3pq4m0xpnawhxb2gs0mn1s0-gcc-12.3.0-lib/lib:$LD_LIBRARY_PATH poetry run uvicorn src.interfaces.api:app --host 0.0.0.0 --port $PORT"
